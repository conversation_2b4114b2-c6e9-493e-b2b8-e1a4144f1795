<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2012 The Netty Project
  ~
  ~ The Netty Project licenses this file to you under the Apache License,
  ~ version 2.0 (the "License"); you may not use this file except in compliance
  ~ with the License. You may obtain a copy of the License at:
  ~
  ~   https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  ~ WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
  ~ License for the specific language governing permissions and limitations
  ~ under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>io.netty</groupId>
    <artifactId>netty-parent</artifactId>
    <version>4.1.124.Final-SNAPSHOT</version>
  </parent>

  <artifactId>netty-all</artifactId>
  <packaging>jar</packaging>

  <name>Netty/All-in-One</name>

  <properties>
    <javaModuleName>io.netty.all</javaModuleName>
    <generatedSourceDir>${project.build.directory}/src</generatedSourceDir>
    <dependencyVersionsDir>${project.build.directory}/versions</dependencyVersionsDir>
    <japicmp.skip>true</japicmp.skip>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-bom</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <profiles>
    <!--
      This profile should be used during the release process together with the "native-dependencies" profile.
      There is no need to do this by hand tho, use the ./scripts/finish_release.sh script.
     -->
    <profile>
      <id>staging</id>
      <repositories>
        <repository>
          <id>staged-releases</id>
          <name>Staged Releases</name>
          <url>https://oss.sonatype.org/service/local/repositories/${stagingRepositoryId}/content/</url>
        </repository>
      </repositories>
    </profile>
    <profile>
      <id>native-dependencies</id>
      <dependencies>
        <!-- Depend on all our native jars -->
        <!-- As this is executed on either macOS or Linux we directly need to specify the classifier -->
        <!-- These dependencies will also be "merged" into the dependency section by the flatten plugin -->
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-transport-native-epoll</artifactId>
          <classifier>linux-x86_64</classifier>
          <scope>runtime</scope>
        </dependency>
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-transport-native-epoll</artifactId>
          <classifier>linux-aarch_64</classifier>
          <scope>runtime</scope>
        </dependency>
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-transport-native-epoll</artifactId>
          <classifier>linux-riscv64</classifier>
          <scope>runtime</scope>
        </dependency>
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-transport-native-kqueue</artifactId>
          <classifier>osx-x86_64</classifier>
          <scope>runtime</scope>
        </dependency>
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-transport-native-kqueue</artifactId>
          <classifier>osx-aarch_64</classifier>
          <scope>runtime</scope>
        </dependency>
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-resolver-dns-native-macos</artifactId>
          <classifier>osx-x86_64</classifier>
          <scope>runtime</scope>
        </dependency>
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-resolver-dns-native-macos</artifactId>
          <classifier>osx-aarch_64</classifier>
          <scope>runtime</scope>
        </dependency>
      </dependencies>
    </profile>
    <profile>
      <id>linux-native-dependencies</id>
      <dependencies>
        <!-- Depend on all our native jars for linux only -->
        <!-- As this is executed on either macOS or Linux we directly need to specify the classifier -->
        <!-- These dependencies will also be "merged" into the dependency section by the flatten plugin -->
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-transport-native-epoll</artifactId>
          <classifier>linux-x86_64</classifier>
          <scope>runtime</scope>
        </dependency>
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-transport-native-epoll</artifactId>
          <classifier>linux-aarch_64</classifier>
          <scope>runtime</scope>
        </dependency>
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-transport-native-epoll</artifactId>
          <classifier>linux-riscv64</classifier>
          <scope>runtime</scope>
        </dependency>
      </dependencies>
    </profile>

    <!-- The linux profile will only include the native jar for epoll to the all jar.
         If you want to also include the native jar for kqueue use -Puber.
    -->
    <profile>
      <!-- Don't active this profile based on the OS we run on so the flatten plugin will not include it -->
      <id>linux</id>
      <dependencies>
        <!-- All release modules -->
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-transport-native-epoll</artifactId>
          <version>${project.version}</version>
          <classifier>${jni.classifier}</classifier>
          <scope>runtime</scope>
        </dependency>
      </dependencies>
    </profile>
    <!-- The mac, openbsd and freebsd  profile will only include the native jar for epol to the all jar.
         If you want to also include the native jar for kqueue use -Puber.
    -->
    <profile>
      <!-- Don't active this profile based on the OS we run on so the flatten plugin will not include it -->
      <id>mac</id>
      <dependencies>
        <!-- All release modules -->
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-transport-native-kqueue</artifactId>
          <version>${project.version}</version>
          <classifier>${jni.classifier}</classifier>
          <scope>runtime</scope>
        </dependency>
        <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>netty-resolver-dns-native-macos</artifactId>
          <version>${project.version}</version>
          <classifier>${jni.classifier}</classifier>
          <scope>runtime</scope>
        </dependency>
      </dependencies>
    </profile>
  </profiles>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>1.2.2</version>
        <configuration>
          <!-- We need to also merge the dependencies defined by our profile. -->
          <embedBuildProfileDependencies>true</embedBuildProfileDependencies>
          <!-- Ensure excludes are set correctly -->
          <flattenDependencyMode>all</flattenDependencyMode>
          <flattenMode>ossrh</flattenMode>
        </configuration>
        <executions>
          <!-- enable flattening -->
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <!-- ensure proper cleanup -->
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.basepom.maven</groupId>
        <artifactId>duplicate-finder-maven-plugin</artifactId>
        <version>1.3.0</version>
        <executions>
          <execution>
            <id>default</id>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <failBuildInCaseOfDifferentContentConflict>true</failBuildInCaseOfDifferentContentConflict>
          <failBuildInCaseOfEqualContentConflict>true</failBuildInCaseOfEqualContentConflict>
          <failBuildInCaseOfConflict>true</failBuildInCaseOfConflict>
          <checkCompileClasspath>true</checkCompileClasspath>
          <checkRuntimeClasspath>true</checkRuntimeClasspath>
          <checkTestClasspath>false</checkTestClasspath>
          <preferLocal>true</preferLocal>
          <useResultFile>true</useResultFile>
          <resultFile>${project.build.directory}/duplicate-finder-result.xml</resultFile>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <!-- All release modules -->
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-buffer</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-dns</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-haproxy</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-http</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-http2</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-memcache</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-mqtt</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-redis</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-smtp</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-socks</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-stomp</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-codec-xml</artifactId>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.fasterxml</groupId>
          <artifactId>aalto-xml</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-common</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-handler</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-handler-proxy</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-handler-ssl-ocsp</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-resolver</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-resolver-dns</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-transport</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-transport-rxtx</artifactId>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.rxtx</groupId>
          <artifactId>rxtx</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-transport-sctp</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-transport-udt</artifactId>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.barchart.udt</groupId>
          <artifactId>barchart-udt-bundle</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--
      The native dependencies with the classifier are added by the native-dependencies profile.
      -->
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-transport-classes-epoll</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-transport-classes-kqueue</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-resolver-dns-classes-macos</artifactId>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>

