/*
 * Copyright 2012 The Netty Project
 *
 * The Netty Project licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
package io.netty.handler.codec.compression;

import io.netty.handler.codec.EncoderException;

/**
 * An {@link EncoderException} that is raised when compression failed.
 */
public class CompressionException extends EncoderException {

    private static final long serialVersionUID = 5603413481274811897L;

    /**
     * Creates a new instance.
     */
    public CompressionException() {
    }

    /**
     * Creates a new instance.
     */
    public CompressionException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Creates a new instance.
     */
    public CompressionException(String message) {
        super(message);
    }

    /**
     * Creates a new instance.
     */
    public CompressionException(Throwable cause) {
        super(cause);
    }
}
