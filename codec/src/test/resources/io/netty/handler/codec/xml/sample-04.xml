<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2012 The Netty Project
  ~
  ~ The Netty Project licenses this file to you under the Apache License,
  ~ version 2.0 (the "License"); you may not use this file except in compliance
  ~ with the License. You may obtain a copy of the License at:
  ~
  ~   https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  ~ WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
  ~ License for the specific language governing permissions and limitations
  ~ under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
  </parent>

  <groupId>io.netty</groupId>
  <artifactId>netty-parent</artifactId>
  <packaging>pom</packaging>
  <version>4.0.14.Final-SNAPSHOT</version>

  <name>Netty</name>
  <url>https://netty.io/</url>
  <description>
    Netty is an asynchronous event-driven network application framework for 
    rapid development of maintainable high performance protocol servers and
    clients.
  </description>

  <organization>
    <name>The Netty Project</name>
    <url>https://netty.io/</url>
  </organization>

  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <inceptionYear>2008</inceptionYear>

  <scm>
    <url>https://github.com/netty/netty</url>
    <connection>scm:git:git://github.com/netty/netty.git</connection>
    <developerConnection>scm:git:ssh://**************/netty/netty.git</developerConnection>
    <tag>HEAD</tag>
  </scm>

  <developers>
    <developer>
      <id>netty.io</id>
      <name>The Netty Project Contributors</name>
      <email><EMAIL></email>
      <url>https://netty.io/</url>
      <organization>The Netty Project</organization>
      <organizationUrl>https://netty.io/</organizationUrl>
    </developer>
  </developers>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <jboss.marshalling.version>1.3.18.GA</jboss.marshalling.version>
    <test.jvm.argLine>
      -server 
      -dsa -da -ea:io.netty...
      -XX:+AggressiveOpts
      -XX:+TieredCompilation
      -XX:+UseBiasedLocking
      -XX:+UseFastAccessorMethods
      -XX:+UseStringCache
      -XX:+OptimizeStringConcat
      -XX:+HeapDumpOnOutOfMemoryError
    </test.jvm.argLine>
  </properties>
 
  <modules>
    <module>common</module>
    <module>buffer</module>
    <module>codec</module>
    <module>codec-http</module>
    <module>codec-socks</module>
    <module>transport</module>
    <module>transport-rxtx</module>
    <module>transport-sctp</module>
    <module>transport-udt</module>
    <module>handler</module>
    <module>example</module>
    <module>testsuite</module>
    <module>microbench</module>
    <module>all</module>
    <module>tarball</module>
  </modules>

  <dependencyManagement>
    <dependencies>
      <!-- JBoss Marshalling - completely optional -->
      <dependency>
        <groupId>org.jboss.marshalling</groupId>
        <artifactId>jboss-marshalling</artifactId>
        <version>${jboss.marshalling.version}</version>
        <scope>compile</scope>
        <optional>true</optional>
      </dependency>
    
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-java</artifactId>
        <version>2.6.1</version>
      </dependency>
      <dependency>
        <groupId>com.jcraft</groupId>
        <artifactId>jzlib</artifactId>
        <version>1.1.3</version>
      </dependency>

      <dependency>
        <groupId>org.rxtx</groupId>
        <artifactId>rxtx</artifactId>
        <version>2.1.7</version>
      </dependency>

      <dependency>
        <groupId>com.barchart.udt</groupId>
        <artifactId>barchart-udt-bundle</artifactId>
        <version>2.3.0</version>
      </dependency>

      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>servlet-api</artifactId>
        <version>2.5</version>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.7.21</version>
      </dependency>
      <dependency>
        <groupId>commons-logging</groupId>
        <artifactId>commons-logging</artifactId>
        <version>1.2</version>
      </dependency>
      <dependency>
        <groupId>log4j</groupId>
        <artifactId>log4j</artifactId>
        <version>1.2.17</version>
        <exclusions>
          <exclusion>
            <artifactId>mail</artifactId>
            <groupId>javax.mail</groupId>
          </exclusion>
          <exclusion>
            <artifactId>jms</artifactId>
            <groupId>javax.jms</groupId>
          </exclusion>
          <exclusion>
            <artifactId>jmxtools</artifactId>
            <groupId>com.sun.jdmk</groupId>
          </exclusion>
          <exclusion>
            <artifactId>jmxri</artifactId>
            <groupId>com.sun.jmx</groupId>
          </exclusion>
        </exclusions>
        <optional>true</optional>
      </dependency>
      
      <!-- Test dependencies for jboss marshalling encoder/decoder -->
      <dependency>
        <groupId>org.jboss.marshalling</groupId>
        <artifactId>jboss-marshalling-serial</artifactId>
        <version>${jboss.marshalling.version}</version>
        <scope>test</scope>
       </dependency>
      <dependency>
        <groupId>org.jboss.marshalling</groupId>
        <artifactId>jboss-marshalling-river</artifactId>
        <version>${jboss.marshalling.version}</version>
        <scope>test</scope>
      </dependency>

      <!-- Test dependencies for microbench -->
      <dependency>
        <groupId>com.google.caliper</groupId>
        <artifactId>caliper</artifactId>
        <version>0.5-rc1</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <!-- Testing frameworks and related dependencies -->
    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymock</artifactId>
      <version>3.4</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymockclassextension</artifactId>
      <version>3.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.jmock</groupId>
      <artifactId>jmock-junit4</artifactId>
      <version>2.8.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>1.1.7</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>1.4.1</version>
        <executions>
          <execution>
            <id>enforce-tools</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireJavaVersion>
                  <!-- Enforce java 1.7 as minimum for compiling -->
                  <!-- This is needed because of java.util.zip.Deflater and NIO UDP multicast-->
                  <version>[1.7.0,)</version>
                </requireJavaVersion>
                <requireMavenVersion>
                  <version>[3.0.5,3.1)</version>
                </requireMavenVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.5.1</version>
        <configuration>
          <compilerVersion>1.7</compilerVersion>
          <fork>true</fork>
          <source>1.6</source>
          <target>1.6</target>
          <debug>true</debug>
          <optimize>true</optimize>
          <showDeprecation>true</showDeprecation>
          <showWarnings>true</showWarnings>
          <compilerArgument>-Xlint:-options</compilerArgument>
          <!-- XXX: maven-release-plugin complains - MRELEASE-715 -->
          <!--
          <compilerArguments>
            <Xlint:-options />
            <Xlint:unchecked />
            <Xlint:deprecation />
          </compilerArguments>
          -->
          <meminitial>256m</meminitial>
          <maxmem>1024m</maxmem>
        </configuration>
      </plugin>
      <plugin>
        <!-- ensure that only methods available in java 1.6 can
             be used even when compiling with java 1.7+ -->
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.15</version>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java16</artifactId>
            <version>1.1</version>
          </signature>
          <ignores>
            <ignore>sun.misc.Unsafe</ignore>
            <ignore>sun.misc.Cleaner</ignore>

            <ignore>java.util.zip.Deflater</ignore>

            <!-- Used for NIO UDP multicast -->
            <ignore>java.nio.channels.DatagramChannel</ignore>
            <ignore>java.nio.channels.MembershipKey</ignore>
            <ignore>java.net.StandardProtocolFamily</ignore>

            <!-- Used for NIO. 2 -->
            <ignore>java.nio.channels.AsynchronousChannel</ignore>
            <ignore>java.nio.channels.AsynchronousSocketChannel</ignore>
            <ignore>java.nio.channels.AsynchronousServerSocketChannel</ignore>
            <ignore>java.nio.channels.AsynchronousChannelGroup</ignore>
            <ignore>java.nio.channels.NetworkChannel</ignore>
            <ignore>java.nio.channels.InterruptedByTimeoutException</ignore>
            <ignore>java.net.StandardSocketOptions</ignore>
            <ignore>java.net.SocketOption</ignore>
          </ignores>
        </configuration>
        <executions>
          <execution>
            <phase>process-classes</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>2.12.1</version>
        <executions>
          <execution>
            <id>check-style</id>
            <goals>
              <goal>check</goal>
            </goals>
            <phase>validate</phase>
            <configuration>
              <consoleOutput>true</consoleOutput>
              <logViolationsToConsole>true</logViolationsToConsole>
              <failsOnError>true</failsOnError>
              <failOnViolation>true</failOnViolation>
              <configLocation>io/netty/checkstyle.xml</configLocation>
              <includeTestSourceDirectory>true</includeTestSourceDirectory>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>netty-build</artifactId>
            <version>19</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <includes>
             <include>**/*Test*.java</include>
             <include>**/*Benchmark*.java</include>
          </includes>
          <excludes>
            <exclude>**/Abstract*</exclude>
            <exclude>**/TestUtil*</exclude>
          </excludes>
          <runOrder>random</runOrder>
          <argLine>${test.jvm.argLine}</argLine>
        </configuration>
      </plugin>
      <!-- always produce osgi bundles -->
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>2.5.4</version>
        <executions>
          <execution>
            <id>generate-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
            <configuration>
              <instructions>
                <Export-Package>${project.groupId}.*</Export-Package>
                <!-- enforce JVM vendor package as optional -->
                <Import-Package>sun.misc.*;resolution:=optional,*</Import-Package>
                <!-- override "internal" private package convention -->
                <Private-Package>!*</Private-Package>
              </instructions>
            </configuration>
          </execution>
        </executions>
      </plugin>             
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.0.1</version>
        <executions>
          <!--
            ~ This workaround prevents Maven from executing the 'generate-sources' phase twice.
            ~ See http://jira.codehaus.org/browse/MSOURCES-13
            ~ and http://blog.peterlynch.ca/2010/05/maven-how-to-prevent-generate-sources.html
            -->
          <execution>
            <id>attach-sources</id>
            <phase>invalid</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
          <execution>
            <id>attach-sources-no-fork</id>
            <phase>package</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.10.4</version>
        <configuration>
          <detectOfflineLinks>false</detectOfflineLinks>
          <breakiterator>true</breakiterator>
          <version>false</version>
          <author>false</author>
          <keywords>true</keywords>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>2.8.2</version>
        <configuration>
          <retryFailedDeploymentCount>10</retryFailedDeploymentCount>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-release-plugin</artifactId>
        <!-- Downgrade to 2.4.1 if release fails -->
        <version>2.5.3</version>
        <configuration>
          <useReleaseProfile>false</useReleaseProfile>
          <arguments>-P release,sonatype-oss-release,full,no-osgi</arguments>
          <autoVersionSubmodules>true</autoVersionSubmodules>
          <allowTimestampedSnapshots>false</allowTimestampedSnapshots>
          <tagNameFormat>netty-@{project.version}</tagNameFormat>
        </configuration>
      </plugin>

      <!-- Ensure to put maven-antrun-plugin at the end of the plugin list
           so that they are run lastly in the same phase. -->
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <!-- Generate the version properties for all artifacts. -->
          <execution>
            <id>write-version-properties</id>
            <phase>initialize</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <taskdef resource="net/sf/antcontrib/antlib.xml" />

                <!-- Get the information about the latest commit -->
                <exec executable="git" outputproperty="gitOutput.shortCommitHash" resultproperty="gitExitCode.shortCommitHash" failonerror="false" failifexecutionfails="false">
                  <arg value="log" />
                  <arg value="-1" />
                  <arg value="--format=format:%h" />
                </exec>
                <if>
                  <equals arg2="0" arg1="${gitExitCode.shortCommitHash}" />
                  <then>
                    <property name="shortCommitHash" value="${gitOutput.shortCommitHash}" />
                  </then>
                  <else>
                    <property name="shortCommitHash" value="0" />
                  </else>
                </if>

                <exec executable="git" outputproperty="gitOutput.longCommitHash" resultproperty="gitExitCode.longCommitHash" failonerror="false" failifexecutionfails="false">
                  <arg value="log" />
                  <arg value="-1" />
                  <arg value="--format=format:%H" />
                </exec>
                <if>
                  <equals arg2="0" arg1="${gitExitCode.longCommitHash}" />
                  <then>
                    <property name="longCommitHash" value="${gitOutput.longCommitHash}" />
                  </then>
                  <else>
                    <property name="longCommitHash" value="0000000000000000000000000000000000000000" />
                  </else>
                </if>

                <exec executable="git" outputproperty="gitOutput.commitDate" resultproperty="gitExitCode.commitDate" failonerror="false" failifexecutionfails="false">
                  <arg value="log" />
                  <arg value="-1" />
                  <arg value="--format=format:%cd" />
                  <arg value="--date=iso" />
                </exec>
                <if>
                  <equals arg2="0" arg1="${gitExitCode.commitDate}" />
                  <then>
                    <property name="commitDate" value="${gitOutput.commitDate}" />
                  </then>
                  <else>
                    <property name="commitDate" value="1970-01-01 00:00:00 +0000" />
                  </else>
                </if>

                <exec executable="git" outputproperty="gitOutput.repoStatus" resultproperty="gitExitCode.repoStatus" failonerror="false" failifexecutionfails="false">
                  <arg value="status" />
                  <arg value="--porcelain" />
                </exec>
                <if>
                  <equals arg2="0" arg1="${gitExitCode.repoStatus}" />
                  <then>
                    <if>
                      <equals arg2="" arg1="${gitOutput.repoStatus}" />
                      <then>
                        <property name="repoStatus" value="clean" />
                      </then>
                      <else>
                        <property name="repoStatus" value="dirty" />
                      </else>
                    </if>
                  </then>
                  <else>
                    <property name="repoStatus" value="unknown" />
                  </else>
                </if>

                <!-- Print the obtained commit information. -->
                <echo>Current commit: ${shortCommitHash} on ${commitDate}</echo>

                <!-- Generate the .properties file. -->
                <!--
                <property name="metaInfDir" value="${project.basedir}/src/main/resources/META-INF" />
                -->
                <property name="metaInfDir" value="${project.build.outputDirectory}/META-INF" />
                <property name="versionPropFile" value="${metaInfDir}/${project.groupId}.versions.properties" />
                <mkdir dir="${metaInfDir}" />
                <delete file="${versionPropFile}" quiet="true" />

                <propertyfile file="${versionPropFile}" comment="Generated by netty-parent/pom.xml">
                  <entry key="${project.artifactId}.version" value="${project.version}" />
                  <entry key="${project.artifactId}.buildDate" type="date" value="now" pattern="yyyy-MM-dd HH:mm:ss Z" />
                  <entry key="${project.artifactId}.commitDate" value="${commitDate}" />
                  <entry key="${project.artifactId}.shortCommitHash" value="${shortCommitHash}" />
                  <entry key="${project.artifactId}.longCommitHash" value="${longCommitHash}" />
                  <entry key="${project.artifactId}.repoStatus" value="${repoStatus}" />
                </propertyfile>
              </target>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>1.9.7</version>
          </dependency>
          <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant-launcher</artifactId>
            <version>1.9.7</version>
          </dependency>
          <dependency>
            <groupId>ant-contrib</groupId>
            <artifactId>ant-contrib</artifactId>
            <version>1.0b3</version>
            <exclusions>
              <exclusion>
                <groupId>ant</groupId>
                <artifactId>ant</artifactId>
              </exclusion>
            </exclusions>
          </dependency>
        </dependencies>
      </plugin>
    </plugins>

    <pluginManagement>
      <plugins>
        <!-- keep surefire and failsafe in sync -->
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.19.1</version>
        </plugin>
        <!-- keep surefire and failsafe in sync -->
        <plugin>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>2.19.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-clean-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.0.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.0.2</version>
          <executions>
            <execution>
              <id>default-jar</id>
              <configuration>
                <archive>
                  <manifest>
                    <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                  </manifest>
                  <index>true</index>
                  <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                </archive>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>2.10</version>
        </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>2.6</version>
        </plugin>
        <plugin>
          <!-- Do NOT upgrade -->
          <artifactId>maven-jxr-plugin</artifactId>
          <version>2.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>1.8</version>
          <dependencies>
            <dependency>
              <groupId>ant-contrib</groupId>
              <artifactId>ant-contrib</artifactId>
              <version>1.0b3</version>
              <exclusions>
                <exclusion>
                  <groupId>ant</groupId>
                  <artifactId>ant</artifactId>
                </exclusion>
              </exclusions>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>1.10</version>
        </plugin>               

        <!-- Workaround for the 'M2E plugin execution not covered' problem.
             See: https://wiki.eclipse.org/M2E_plugin_execution_not_covered -->
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <versionRange>[1.7,)</versionRange>
                    <goals>
                      <goal>run</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <versionRange>[1.0,)</versionRange>
                    <goals>
                      <goal>check</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <execute>
                      <runOnIncremental>false</runOnIncremental>
                    </execute>
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <versionRange>[1.0,)</versionRange>
                    <goals>
                      <goal>enforce</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <execute>
                      <runOnIncremental>false</runOnIncremental>
                    </execute>
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <versionRange>[1.0,)</versionRange>
                    <goals>
                      <goal>clean</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <execute>
                      <runOnIncremental>false</runOnIncremental>
                    </execute>
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <versionRange>[2.4,)</versionRange>
                    <goals>
                      <goal>manifest</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
</project>